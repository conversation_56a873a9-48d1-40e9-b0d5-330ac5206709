#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面优化验证脚本
"""

import os
import re

def check_template_optimization():
    """检查模板文件优化"""
    print("=" * 60)
    print("检查模板文件优化")
    print("=" * 60)
    
    # 检查气密性模板
    airtightness_template = "templates/airtightness/airtightness_comparison.html"
    print(f"\n1. 检查 {airtightness_template}")
    
    if os.path.exists(airtightness_template):
        with open(airtightness_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否删除了标签文字
        if '选择车型进行对比:' not in content:
            print("   ✓ 已删除标签文字")
        else:
            print("   ✗ 标签文字仍存在")
        
        # 检查是否使用了align-items-end
        if 'align-items-end' in content:
            print("   ✓ 使用了底部对齐")
        else:
            print("   ✗ 未使用底部对齐")
        
        # 检查列宽比例
        if 'col-md-8' in content and 'col-md-4' in content:
            print("   ✓ 列宽比例正确 (8:4)")
        else:
            print("   ✗ 列宽比例不正确")
    else:
        print("   ✗ 文件不存在")
    
    # 检查吸隔声模板
    sound_template = "templates/sound_insulation/area_comparison.html"
    print(f"\n2. 检查 {sound_template}")
    
    if os.path.exists(sound_template):
        with open(sound_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否删除了标签文字
        labels_removed = ('选择车型进行对比:' not in content and 
                         '测试区域:' not in content)
        if labels_removed:
            print("   ✓ 已删除标签文字")
        else:
            print("   ✗ 标签文字仍存在")
        
        # 检查是否使用了align-items-end
        if 'align-items-end' in content:
            print("   ✓ 使用了底部对齐")
        else:
            print("   ✗ 未使用底部对齐")
        
        # 检查列宽比例
        if 'col-md-3' in content and 'col-md-6' in content:
            print("   ✓ 列宽比例正确 (3:6:3)")
        else:
            print("   ✗ 列宽比例不正确")
    else:
        print("   ✗ 文件不存在")

def check_javascript_optimization():
    """检查JavaScript文件优化"""
    print("\n" + "=" * 60)
    print("检查JavaScript文件优化")
    print("=" * 60)
    
    # 检查气密性JS
    airtightness_js = "static/js/airtightness.js"
    print(f"\n1. 检查 {airtightness_js}")
    
    if os.path.exists(airtightness_js):
        with open(airtightness_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了车型代码
        code_patterns = [
            r'\$\{vehicle\.name\} \(\$\{vehicle\.code\}\)',
            r'vehicle\.name.*vehicle\.code'
        ]
        
        code_found = any(re.search(pattern, content) for pattern in code_patterns)
        if not code_found:
            print("   ✓ 已移除车型代码显示")
        else:
            print("   ✗ 仍包含车型代码显示")
        
        # 检查是否只显示车型名称
        if '${vehicle.name}' in content and '(${vehicle.code})' not in content:
            print("   ✓ 只显示车型名称")
        else:
            print("   ✗ 车型显示格式不正确")
    else:
        print("   ✗ 文件不存在")
    
    # 检查吸隔声JS
    sound_js = "static/js/sound_insulation.js"
    print(f"\n2. 检查 {sound_js}")
    
    if os.path.exists(sound_js):
        with open(sound_js, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了车型代码
        if '(${vehicle.code})' not in content:
            print("   ✓ 已移除车型代码显示")
        else:
            print("   ✗ 仍包含车型代码显示")
        
        # 检查是否只显示车型名称
        if '${vehicle.name}' in content:
            print("   ✓ 只显示车型名称")
        else:
            print("   ✗ 车型显示格式不正确")
    else:
        print("   ✗ 文件不存在")

def check_css_optimization():
    """检查CSS文件优化"""
    print("\n" + "=" * 60)
    print("检查CSS文件优化")
    print("=" * 60)
    
    # 检查气密性CSS
    airtightness_css = "static/css/airtightness.css"
    print(f"\n1. 检查 {airtightness_css}")
    
    if os.path.exists(airtightness_css):
        with open(airtightness_css, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局优化样式
        if '.row.align-items-end' in content:
            print("   ✓ 添加了布局优化样式")
        else:
            print("   ✗ 缺少布局优化样式")
        
        # 检查响应式设计
        if '@media (max-width: 768px)' in content:
            print("   ✓ 包含响应式设计")
        else:
            print("   ✗ 缺少响应式设计")
        
        # 检查按钮高度统一
        if 'height: 38px' in content:
            print("   ✓ 按钮高度统一")
        else:
            print("   ✗ 按钮高度未统一")
    else:
        print("   ✗ 文件不存在")
    
    # 检查吸隔声CSS
    sound_css = "static/css/sound_insulation.css"
    print(f"\n2. 检查 {sound_css}")
    
    if os.path.exists(sound_css):
        with open(sound_css, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查布局优化样式
        if '.row.align-items-end' in content:
            print("   ✓ 添加了布局优化样式")
        else:
            print("   ✗ 缺少布局优化样式")
        
        # 检查响应式设计
        if '@media (max-width: 768px)' in content:
            print("   ✓ 包含响应式设计")
        else:
            print("   ✗ 缺少响应式设计")
        
        # 检查按钮高度统一
        if 'height: 38px' in content:
            print("   ✓ 按钮高度统一")
        else:
            print("   ✗ 按钮高度未统一")
    else:
        print("   ✗ 文件不存在")

def main():
    """主函数"""
    print("界面优化验证")
    print("=" * 60)
    
    # 切换到项目目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 执行各项检查
    check_template_optimization()
    check_javascript_optimization()
    check_css_optimization()
    
    print("\n" + "=" * 60)
    print("验证完成")
    print("=" * 60)
    print("\n访问以下URL进行实际测试:")
    print("- 气密性页面: http://localhost:5000/airtightness/comparison")
    print("- 吸隔声页面: http://localhost:5000/sound_insulation/area_comparison")
    print("\n注意事项:")
    print("1. 清除浏览器缓存以确保加载最新文件")
    print("2. 测试不同屏幕尺寸的响应式效果")
    print("3. 验证所有交互功能正常工作")

if __name__ == '__main__':
    main()
